<!doctype html>
<html lang="zh-TW">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>知識對話小幫手</title>
    <style>
      * {
        box-sizing: border-box;
      }

      body {
        margin: 0;
        font-family: "Noto Sans TC", Arial, sans-serif;
        /* background-color: #eef2f7; */
        background: url(Ai3.png);
        background-size: cover;
        height: 100vh;
      }

      .crm-area {
        flex: 1;
        background: #f3f6fb;
      }

      .history-panel {
        width: 229px;
        height: 100%;
        background-color: #ffffff;
        border-left: 1px solid #c8d2e0;
        border-right: 1px solid #c8d2e0;
        display: flex;
        flex-direction: column;
      }

      .history-header {
        height: 41px;
        background-color: #1f4379;
        color: white;
        font-size: 14px;
        display: flex;
        align-items: center;
        padding-left: 12px;
      }

      .history-content {
        flex: 1;
        overflow-y: auto;
        font-size: 14px;
      }

      .history-item {
        padding: 10px 12px;
        border-bottom: 1px solid #e5e5e5;
        display: flex;
        justify-content: space-between;
        cursor: pointer;
      }

      .chatbot-container {
        width: 460px;
        height: 100%;
        background-color: #ffffff;
        border-left: 1px solid #c8d2e0;
        display: flex;
        flex-direction: column;
        position: relative;
      }

      .chatbot-header {
        height: 41px;
        background-color: #1f4379;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 12px;
        color: white;
        font-size: 14px;
      }

      .chatbot-header .chatbot-menus {
        display: flex;
        align-items: center;
      }

      .chatbot-header .chatbot-menus span {
        padding-left: 10px;
      }

      .chat-body {
        flex: 1;
        overflow-y: auto;
        padding: 12px;
      }

      .chat-message {
        margin-bottom: 16px;
      }

      .chat-bubble-group {
        padding-bottom: 30px;
      }

      .chat-bubble {
        display: inline-block;
        max-width: 60%;
        padding: 10px 12px;
        border-radius: 10px;
        font-size: 14px;
        word-break: break-word;
      }

      .chat-bubble.bot {
        float: left;
        background-color: #f1f1f1;
        text-align: left;
      }

      .chat-time.bot {
        float: left;
        font-size: 10px;
        color: #888;
        margin-top: 25px;
      }

      .chat-bubble.user {
        float: right;
        background-color: #d3ecff;
        margin-left: auto;
        text-align: right;
      }

      .chat-time.user {
        float: right;
        font-size: 10px;
        color: #888;
        margin-top: 25px;
      }

      .chat-input {
        border-top: 1px solid #ccc;
        padding: 8px 12px;
        display: flex;
        flex-direction: column;
        gap: 6px;
      }

      .chat-input-row1 {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .chat-input-row1 input {
        flex: 1;
        padding: 8px 10px;
        font-size: 14px;
        border: none;
        outline: none;
      }

      .chat-input-row1 .input-right {
        display: flex;
        gap: 8px;
        align-items: center;
        color: #888;
        font-size: 12px;
      }

      .chat-input-row2 {
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-radius: 6px;
        padding: 6px 8px;
      }

      .chat-actions-left {
        display: flex;
        gap: 12px;
      }

      .chat-actions-left img {
        width: 20px;
        height: 20px;
        cursor: pointer;
      }

      .chat-actions-right {
        display: flex;
        align-items: center;
        gap: 6px;
      }

      .chat-actions-right select {
        border: none;
        background: transparent;
        font-size: 14px;
        outline: none;
      }

      .chat-actions-right button {
        background: #3182f6;
        border: none;
        border-radius: 50%;
        width: 28px;
        height: 28px;
        color: white;
        font-size: 18px;
        cursor: pointer;
      }

      @media screen and (max-width: 768px) {
        .history-panel {
          display: none;
        }

        .chatbot-container {
          width: 100%;
        }
      }

      .chatbot-v-align {
        display: flex;
        justify-content: center;
      }

      .chatbot-history-icon {
        padding-right: 8px;
      }

      .chatbot-template {
        display: flex;
        float: right;
        height: 98vh;
        z-index: 999;
      }
    </style>
  </head>

  <body>
    <div class="crm-area"></div>
    <!-- 
      維持舊版使用 layer 方式獨立於當前頁面 左方是任何頁面, 右方是 .chatbot-template 內才是 ChatBot 設計樣式
      init 預設:
        ChatBot 寬度 460px 左方歷史區 寬度 229px 一開始預設不顯示左方歷史區
        版面預設為 小 中 大 的 小 , chat-bubble 最大寬度 請調整為 ChatBot 版面 4/3
        字體預設為 小 中 大 的 小
    -->
    <div class="chatbot-template" reversion="ChatBot 設計樣式開始">
      <div class="history-panel">
        <div class="history-header" reversion="歷史紀錄側邊欄圖示 開啟後 toggle ChatBot 上方顯示的圖示">
          <svg width="20" height="21" viewBox="0 0 20 21" fill="none" xmlns="http://www.w3.org/2000/svg">
            <rect x="0.5" y="1" width="19" height="19" rx="2.5" stroke="white" />
            <path d="M7.5 1.5L7.5 19.5" stroke="white" />
            <path d="M2.5 4.5H5.5" stroke="white" stroke-linecap="round" />
            <path d="M2.5 7.5H5.5" stroke="white" stroke-linecap="round" />
          </svg>
        </div>
        <div class="history-content">
          <div class="history-item">
            <span reversion="歷史 Item 文字">今天：切換語言背景問題</span>
            <span reversion="歷史 Item 選項 點選後顯示 [重新命名] [刪除]">
              <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                <circle cx="3" cy="10" r="2" fill="black" />
                <circle cx="10" cy="10" r="2" fill="black" />
                <circle cx="17" cy="10" r="2" fill="black" />
              </svg>
            </span>
          </div>
          <div class="history-item">
            <span>昨天：HTML語法化解析</span>
            <span>
              <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                <circle cx="3" cy="10" r="2" fill="black" />
                <circle cx="10" cy="10" r="2" fill="black" />
                <circle cx="17" cy="10" r="2" fill="black" />
              </svg>
            </span>
          </div>
          <div class="history-item">
            <span>過去七天：XSS濫用修補建議</span>
            <span>
              <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                <circle cx="3" cy="10" r="2" fill="black" />
                <circle cx="10" cy="10" r="2" fill="black" />
                <circle cx="17" cy="10" r="2" fill="black" />
              </svg>
            </span>
          </div>
        </div>
      </div>
      <div class="chatbot-container">
        <div class="chatbot-header">
          <div class="chatbot-v-align">
            <span class="chatbot-history-icon" reversion="歷史紀錄側邊欄圖示 開啟歷史紀錄側邊後不顯示">
              <svg width="20" height="21" viewBox="0 0 20 21" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect x="0.5" y="1" width="19" height="19" rx="2.5" stroke="white" />
                <path d="M7.5 1.5L7.5 19.5" stroke="white" />
                <path d="M2.5 4.5H5.5" stroke="white" stroke-linecap="round" />
                <path d="M2.5 7.5H5.5" stroke="white" stroke-linecap="round" />
              </svg>
            </span>
            <span>知識對話小幫手</span>
          </div>
          <div class="chatbot-menus">
            <span reversion="調整版面寬度 小 中 大 循環式切換">
              <svg width="20" height="21" viewBox="0 0 20 21" fill="none" xmlns="http://www.w3.org/2000/svg">
                <mask id="path-1-inside-1_23_513" fill="white">
                  <path d="M0 8.48V7.5C0 2.5 2 0.5 7 0.5H13C18 0.5 20 2.5 20 7.5V13.5C20 18.5 18 20.5 13 20.5H12" />
                </mask>
                <path d="M-1 8.48C-1 9.03228 -0.552285 9.48 0 9.48C0.552285 9.48 1 9.03228 1 8.48H-1ZM12 19.5C11.4477 19.5 11 19.9477 11 20.5C11 21.0523 11.4477 21.5 12 21.5V19.5ZM1 8.48V7.5H-1V8.48H1ZM1 7.5C1 5.09863 1.4864 3.67782 2.33211 2.83211C3.17782 1.9864 4.59863 1.5 7 1.5V-0.5C4.40137 -0.5 2.32218 0.0136016 0.917893 1.41789C-0.486398 2.82218 -1 4.90137 -1 7.5H1ZM7 1.5H13V-0.5H7V1.5ZM13 1.5C15.4014 1.5 16.8222 1.9864 17.6679 2.83211C18.5136 3.67782 19 5.09863 19 7.5H21C21 4.90137 20.4864 2.82218 19.0821 1.41789C17.6778 0.0136016 15.5986 -0.5 13 -0.5V1.5ZM19 7.5V13.5H21V7.5H19ZM19 13.5C19 15.9014 18.5136 17.3222 17.6679 18.1679C16.8222 19.0136 15.4014 19.5 13 19.5V21.5C15.5986 21.5 17.6778 20.9864 19.0821 19.5821C20.4864 18.1778 21 16.0986 21 13.5H19ZM13 19.5H12V21.5H13V19.5Z" fill="white" mask="url(#path-1-inside-1_23_513)" />
                <path d="M10 9.5L16 3.5H11.1976" stroke="white" stroke-linecap="round" stroke-linejoin="round" />
                <path d="M16 3.5V8.5" stroke="white" stroke-linecap="round" stroke-linejoin="round" />
                <path d="M3.81714 11.3333H6.51636C7.59204 11.3333 8.21518 11.5517 8.58179 11.9183C8.94839 12.2849 9.16675 12.908 9.16675 13.9837V16.6829C9.16675 17.7586 8.94839 18.3817 8.58179 18.7484C8.21518 19.115 7.59204 19.3333 6.51636 19.3333H3.81714C2.74145 19.3333 2.11831 19.115 1.75171 18.7484C1.3851 18.3817 1.16675 17.7586 1.16675 16.6829V13.9837L1.17651 13.6009C1.22328 12.7559 1.43091 12.2391 1.75171 11.9183C2.11831 11.5517 2.74145 11.3333 3.81714 11.3333Z" stroke="white" stroke-linecap="round" stroke-linejoin="round" />
              </svg>
            </span>
            <span reversion="調整字體 小 中 大 循環式切換">
              <svg width="20" height="21" viewBox="0 0 20 21" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M1 13.2C1 16.683 3.817 19.5 7.3 19.5L6.355 17.925" stroke="white" stroke-linecap="round" stroke-linejoin="round" />
                <path d="M19 7.8C19 4.317 16.183 1.5 12.7 1.5L13.645 3.075" stroke="white" stroke-linecap="round" stroke-linejoin="round" />
                <path d="M2 12.1452L6.19048 1.5L8.89401 9.30645M10 12.5L8.89401 9.30645M3.14286 9.30645C3.44762 9.30645 7.10394 9.30645 8.89401 9.30645" stroke="white" stroke-width="1.5" stroke-linejoin="round" />
                <path d="M13 19.2419L16.1429 11.5L18.1705 17.1774M19 19.5L18.1705 17.1774M13.8571 17.1774C14.0857 17.1774 16.828 17.1774 18.1705 17.1774" stroke="white" stroke-width="1.5" stroke-linejoin="round" />
              </svg>
            </span>
            <span reversion="其他選項按鈕 目前有 [登出] 一個選項">
              <svg width="20" height="21" viewBox="0 0 20 21" fill="none" xmlns="http://www.w3.org/2000/svg">
                <circle cx="3" cy="10.5" r="2" fill="white" />
                <circle cx="10" cy="10.5" r="2" fill="white" />
                <circle cx="17" cy="10.5" r="2" fill="white" />
              </svg>
            </span>
            <span reversion="隱藏 ChatBot">
              <svg width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
                <g filter="url(#filter0_d_438_3993)">
                  <circle cx="12" cy="12.5" r="10" fill="white" />
                </g>
                <path d="M17.3791 6.58217C17.5731 6.45414 17.8372 6.4758 18.008 6.64662C18.1787 6.81743 18.2004 7.08147 18.0724 7.27553L18.008 7.35365L12.7834 12.5773L18.0089 17.8019C18.2042 17.9971 18.204 18.3137 18.0089 18.5089C17.8137 18.7042 17.4972 18.7042 17.3019 18.5089L12.0773 13.2843L6.85367 18.5089C6.65841 18.7042 6.34189 18.7042 6.14663 18.5089C5.95151 18.3136 5.95142 17.9971 6.14663 17.8019L11.3693 12.5773L6.14663 7.35463L6.08218 7.2765C5.95412 7.08245 5.97581 6.81841 6.14663 6.6476C6.31747 6.47688 6.58151 6.45511 6.77554 6.58314L6.85367 6.6476L12.0763 11.8703L17.3009 6.64662L17.3791 6.58217Z" fill="black" />
                <defs>
                  <filter id="filter0_d_438_3993" x="0" y="0.5" width="24" height="24" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                    <feFlood flood-opacity="0" result="BackgroundImageFix" />
                    <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha" />
                    <feOffset />
                    <feGaussianBlur stdDeviation="1" />
                    <feComposite in2="hardAlpha" operator="out" />
                    <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0" />
                    <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_438_3993" />
                    <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_438_3993" result="shape" />
                  </filter>
                </defs>
              </svg>
            </span>
          </div>
        </div>
        <div class="chat-body">
          <div class="chat-message">
            <div class="chat-bubble-group">
              <div class="chat-bubble bot">Hey, Lisa! How’s your day going?</div>
              <div class="chat-time bot">mm:ss</div>
            </div>
          </div>
          <div class="chat-message">
            <div class="chat-bubble-group">
              <div class="chat-bubble user">Hey, Lisa! How’s your day going?</div>
              <div class="chat-time user">mm:ss</div>
            </div>
          </div>
        </div>
        <div class="chat-input">
          <div class="chat-input-row1">
            <input type="text" placeholder="想要問什麼呢？" maxlength="1000" />
            <div class="input-right">0/1000 <img src="https://cdn-icons-png.flaticon.com/512/724/724715.png" width="20" /></div>
          </div>
          <div class="chat-input-row2">
            <div class="chat-actions-left">
              <span reversion="舊版本的 [自動辨識] 按鈕 操作行為: setp1，mouse hover出現Tip提示按鈕內容。 setp2，mouse click按鈕變藍向右展開完整且出現選單。 setp3，選擇選單中其中一個功能。 setp4，執行所選功能。 取消選取 1、已選中的按鈕在點一次就取消選取。 2、點擊其他按鈕(一次只能使用一個功能)。">
                <svg width="34" height="34" viewBox="0 0 34 34" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <g filter="url(#filter0_d_208_2406)">
                    <circle cx="17" cy="17" r="15" fill="white" />
                  </g>
                  <path d="M14.9735 8.83463C15.0961 8.72508 15.3035 8.69458 15.4305 8.75289C15.6098 8.83537 15.7226 8.99358 15.7411 9.15316L15.7412 9.15845L16.222 12.9352L16.2404 13.0792L16.3317 13.19L18.7387 16.1175L18.7431 16.123C18.8577 16.2586 18.8885 16.4285 18.834 16.588C18.7604 16.743 18.6741 16.8251 18.5785 16.8687C18.5729 16.8713 18.5657 16.875 18.5459 16.8793C18.5341 16.8818 18.5191 16.8838 18.4973 16.8875C18.48 16.8904 18.4518 16.8953 18.4251 16.9L14.7672 17.3939L14.6218 17.414L14.5082 17.5108L11.6503 19.9631L11.643 19.9707C11.5177 20.0827 11.3588 20.1158 11.2043 20.0581C11.0338 19.9739 10.9269 19.8209 10.9089 19.6659L10.9084 19.6597L10.4276 15.8829L10.4099 15.7407L10.3199 15.6305L7.93645 12.6933L7.92992 12.6855L7.88944 12.631C7.80814 12.5056 7.78979 12.3582 7.83744 12.2191C7.91828 12.0426 8.06279 11.9386 8.2035 11.9213L8.2096 11.9206L11.8824 11.4243L12.0304 11.4051L12.1445 11.3048L14.9689 8.83885L14.9735 8.83463Z" stroke="black" />
                  <path d="M24.7956 18.8929L26.3337 17.4709C26.4796 17.3391 26.5357 17.1333 26.501 16.939C26.4663 16.7447 26.3017 16.5715 26.1137 16.5214L24.1168 15.9636L22.7331 14.3807C22.6049 14.2306 22.4048 14.1728 22.2159 14.2083C22.0195 14.2563 21.8783 14.4082 21.8297 14.6016L21.2691 16.6598L19.731 18.0818C19.5851 18.2135 19.5291 18.4194 19.5637 18.6137C19.6105 18.8157 19.7583 18.961 19.9463 19.0111L21.948 19.5891L23.3316 21.172C23.3728 21.2153 23.4018 21.2509 23.4504 21.2817C23.572 21.3587 23.7114 21.378 23.861 21.3521C24.0574 21.3041 24.1986 21.1521 24.2472 20.9588L24.7956 18.8929Z" fill="black" />
                  <path d="M18.7777 22.0169L18.5581 20.2902C18.5393 20.1279 18.4303 19.9936 18.2868 19.9276C18.1433 19.8615 17.9534 19.8956 17.8335 20.0028L16.5421 21.1305L14.8628 21.3572C14.705 21.3766 14.5743 21.4888 14.51 21.6364C14.4505 21.795 14.4847 21.9634 14.5889 22.0867L15.6788 23.4298L15.8984 25.1566C15.9172 25.3189 16.0262 25.4532 16.1697 25.5192C16.3238 25.5803 16.4876 25.5451 16.6076 25.4378L17.9144 24.3163L19.5937 24.0895C19.6412 24.081 19.678 24.0774 19.7207 24.0579C19.8276 24.0092 19.9048 23.9214 19.9572 23.8055C20.0167 23.6469 19.9825 23.4785 19.8783 23.3552L18.7777 22.0169Z" fill="black" />
                  <defs>
                    <filter id="filter0_d_208_2406" x="0" y="0" width="34" height="34" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                      <feFlood flood-opacity="0" result="BackgroundImageFix" />
                      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha" />
                      <feOffset />
                      <feGaussianBlur stdDeviation="1" />
                      <feComposite in2="hardAlpha" operator="out" />
                      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0" />
                      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_208_2406" />
                      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_208_2406" result="shape" />
                    </filter>
                  </defs>
                </svg>
              </span>
              <span reversion="新版本 [AI幫幫我] 選擇後的類型 Icon">
                <svg width="34" height="34" viewBox="0 0 34 34" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <g filter="url(#filter0_d_208_2346)">
                    <circle cx="17" cy="17" r="15" fill="white" />
                  </g>
                  <path d="M9.12109 7.50586V7.50488C10.115 7.59255 11.3919 7.9069 12.6465 8.35156C13.9016 8.79643 15.0983 9.35984 15.9375 9.9248H15.9385L16.1885 10.0928C16.1933 10.096 16.1982 10.0995 16.2031 10.1025C16.4486 10.2548 16.7416 10.3164 17.0059 10.3164C17.2687 10.3164 17.5633 10.255 17.8066 10.0957L17.8145 10.0908L17.9873 9.97266L17.9863 9.97168C18.8355 9.4058 20.037 8.83707 21.292 8.38477C22.5427 7.934 23.8112 7.61388 24.8008 7.52441H24.8369C24.8509 7.52441 24.865 7.52363 24.8789 7.52246C25.7472 7.44902 26.5 8.12464 26.5 9.15723V22.0713C26.4999 22.8909 25.8751 23.6068 25.1846 23.6904L25.1689 23.6924L24.8398 23.7432C22.5985 24.072 19.168 25.2681 17.1982 26.4443L17.1768 26.459C17.1554 26.4733 17.0875 26.5 16.9824 26.5C16.8789 26.5 16.8003 26.4736 16.7646 26.4512L16.667 26.4033L16.6572 26.3994C14.6902 25.2545 11.3259 24.0591 9.1123 23.7451L8.82812 23.6953C8.8192 23.6937 8.80978 23.6915 8.80078 23.6904C8.12945 23.6078 7.50011 22.8789 7.5 22.0713V9.13965C7.50022 8.12131 8.25553 7.43264 9.12109 7.50586Z" stroke="black" stroke-linecap="round" stroke-linejoin="round" />
                  <mask id="path-3-inside-1_208_2346" fill="white">
                    <path d="M17 11.2857L17 25.5714L17 11.2857Z" />
                  </mask>
                  <path d="M17.5 11.2857C17.5 11.0096 17.2761 10.7857 17 10.7857C16.7239 10.7857 16.5 11.0096 16.5 11.2857L17.5 11.2857ZM16.5 25.5714C16.5 25.8476 16.7239 26.0714 17 26.0714C17.2761 26.0714 17.5 25.8476 17.5 25.5714L16.5 25.5714ZM17 11.2857L16.5 11.2857L16.5 25.5714L17 25.5714L17.5 25.5714L17.5 11.2857L17 11.2857Z" fill="black" mask="url(#path-3-inside-1_208_2346)" />
                  <defs>
                    <filter id="filter0_d_208_2346" x="0" y="0" width="34" height="34" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                      <feFlood flood-opacity="0" result="BackgroundImageFix" />
                      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha" />
                      <feOffset />
                      <feGaussianBlur stdDeviation="1" />
                      <feComposite in2="hardAlpha" operator="out" />
                      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0" />
                      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_208_2346" />
                      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_208_2346" result="shape" />
                    </filter>
                  </defs>
                </svg>
              </span>
            </div>
            <div class="chat-actions-right">
              <select>
                <option>知識庫</option>
              </select>
              <button title="送出">↑</button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </body>
</html>
